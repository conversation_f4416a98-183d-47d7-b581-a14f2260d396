# 优惠券系统使用说明书

## 1. 系统概述

本优惠券系统是基于yh-vegan-admin-web前端和yh-vegan-admin后端开发的完整优惠券管理解决方案。系统提供了优惠券的定义、创建、批次管理、发放和使用等全生命周期管理功能。

### 1.1 主要功能模块
- **优惠券管理**：创建、编辑、删除优惠券
- **批次管理**：按批次管理优惠券的发放
- **发放管理**：手动或自动发放优惠券给用户
- **使用记录**：跟踪优惠券的使用情况
- **发券规则**：配置自动发券规则

### 1.2 系统架构
- **前端**：Vue.js 2.x + Element UI
- **后端**：FastAPI + SQLAlchemy
- **数据库**：PostgreSQL

## 2. 优惠券定义和类型

### 2.1 优惠券类型

系统支持三种主要的优惠券类型：

#### 2.1.1 现金券 (Cash Coupon)
- **定义**：直接抵扣固定金额的优惠券
- **参数**：`amount` - 抵扣金额
- **计算方式**：订单金额 - 抵扣金额
- **示例**：20元现金券，订单100元，优惠后80元

#### 2.1.2 折扣券 (Discount Coupon)
- **定义**：按比例折扣的优惠券
- **参数**：
  - `discount_rate`：折扣比例（0-1之间）
  - `max_discount`：最大优惠金额（0表示无限制）
- **计算方式**：订单金额 × 折扣比例
- **示例**：8折券，订单100元，优惠后80元

#### 2.1.3 满减券 (Full Reduction Coupon)
- **定义**：满足条件金额后减免固定金额
- **参数**：
  - `full_amount`：满足金额
  - `reduction_amount`：减免金额
- **计算方式**：当订单金额 ≥ 满足金额时，订单金额 - 减免金额
- **示例**：满100减20，订单130元，优惠后110元

### 2.2 使用条件规则

#### 2.2.1 条件范围 (condition_scope)
- **订单级别**：条件作用于整个订单
- **产品级别**：条件作用于指定产品

#### 2.2.2 条件对象 (condition_objects)
- 指定优惠券适用的产品ID和数量组合
- 格式：`[{"product_id": quantity}]`

#### 2.2.3 条件金额 (condition_amount)
- 订单总金额需要达到的最低金额
- 0表示无金额限制

#### 2.2.4 条件产品类型 (condition_product_types)
- 指定优惠券适用的产品餐食类型
- 支持多选

### 2.3 使用限制约束

#### 2.3.1 使用周期 (usage_cycle)
- **每订单**：每个订单只能使用一次
- **每日**：每天限制使用次数
- **每周**：每周限制使用次数
- **每月**：每月限制使用次数
- **每年**：每年限制使用次数

#### 2.3.2 使用数量 (usage_quantity)
- 在指定周期内可使用的次数
- 0表示无数量限制

### 2.4 应用约束规则

#### 2.4.1 应用范围 (apply_scope)
- **订单**：优惠作用于整个订单
- **产品**：优惠作用于指定产品

#### 2.4.2 应用对象 (apply_objects)
- 指定优惠作用的产品对象和数量
- 格式：`[{"product_id": quantity}]`

#### 2.4.3 互斥规则 (mutual_exclusive_rules)
- 指定不能同时使用的优惠券ID列表
- 确保优惠券之间的互斥性

## 3. 优惠券创建流程

### 3.1 访问优惠券管理页面
1. 登录管理后台
2. 导航至「优惠券」→「优惠券列表」
3. 点击「新增优惠券」按钮

### 3.2 填写基本信息

#### 3.2.1 必填字段
- **优惠券类型**：选择现金券、折扣券或满减券
- **优惠券名称**：输入优惠券的名称
- **优惠券描述**：详细描述优惠券的用途和规则
- **优惠券状态**：启用或禁用

#### 3.2.2 类型特定字段

**现金券**：
- 抵扣金额：设置固定抵扣金额

**折扣券**：
- 折扣比例：设置0-1之间的折扣比例
- 最大优惠金额：设置优惠上限（可选）

**满减券**：
- 满足金额：设置触发优惠的最低金额
- 减免金额：设置减免的固定金额

### 3.3 配置使用规则

#### 3.3.1 条件设置
1. **条件范围**：选择订单或产品级别
2. **条件产品类型**：选择适用的餐食类型
3. **条件对象**：点击「选择产品」添加指定产品
4. **条件金额**：设置最低消费金额

#### 3.3.2 使用限制
1. **使用数量**：设置周期内可使用次数
2. **使用周期**：选择限制周期类型
3. **使用限制**：设置总使用次数限制

#### 3.3.3 应用规则
1. **应用范围**：选择优惠作用范围
2. **应用对象**：选择优惠作用的具体产品
3. **互斥规则**：选择不能同时使用的其他优惠券

### 3.4 保存和启用
1. 点击「保存」按钮创建优惠券
2. 确认优惠券状态为「已启用」
3. 在优惠券列表中验证创建结果

## 4. 优惠券批次管理

### 4.1 批次管理概述

批次管理是优惠券系统的核心功能，用于控制优惠券的发放数量、时间和渠道。每个优惠券可以创建多个批次，每个批次有独立的发放规则。

### 4.2 创建优惠券批次

#### 4.2.1 访问批次管理
1. 在优惠券列表页面点击「批次管理」
2. 或点击特定优惠券的「查看批次」
3. 点击「新增批次」按钮

#### 4.2.2 基本信息设置
- **批次名称**：输入批次的识别名称
- **批次描述**：详细描述批次用途
- **关联优惠券**：选择要创建批次的优惠券
- **批次数量**：设置本批次的优惠券总数量
- **批次状态**：启用或禁用

#### 4.2.3 时间设置
- **生效时间**：批次开始生效的时间
- **结束时间**：批次结束的时间
- **有效时长**：从领取时开始计算的有效小时数

#### 4.2.4 发放设置
- **发放渠道**：选择允许发放的渠道
  - 新人注册
  - 浏览活动
  - 分享活动
  - 销售购买
- **周期发放数量**：每个发放周期的数量限制
- **发放周期**：发放的时间周期

#### 4.2.5 领取设置
- **领取开始时间**：用户可以开始领取的时间
- **领取结束时间**：用户停止领取的时间
- **周期可领取数量**：用户在周期内可领取的数量
- **领取周期**：领取限制的时间周期

### 4.3 批次管理操作

#### 4.3.1 查看批次列表
- 批次ID、名称、数量
- 有效期时间范围
- 发放渠道标签
- 批次状态

#### 4.3.2 批次操作
- **编辑**：修改批次信息
- **启用/禁用**：切换批次状态
- **删除**：删除不需要的批次

#### 4.3.3 批次筛选
- 按批次名称搜索
- 按状态筛选
- 按关联优惠券筛选

## 5. 优惠券发放功能

### 5.1 手动发放优惠券

#### 5.1.1 访问发放功能
1. 导航至「优惠券」→「优惠券使用记录」
2. 点击「发放优惠券」按钮

#### 5.1.2 发放步骤
1. **选择优惠券**：
   - 在下拉框中搜索优惠券名称
   - 支持模糊搜索和远程加载

2. **选择批次**：
   - 根据选择的优惠券自动加载可用批次
   - 显示批次名称和剩余数量
   - 只能选择有剩余数量的批次

3. **选择用户**：
   - 支持多选用户
   - 可按用户名或真实姓名搜索
   - 支持远程搜索加载

4. **确认发放**：
   - 检查选择信息
   - 点击「发放」按钮执行

#### 5.1.3 发放验证
- 系统会验证批次的有效性
- 检查批次剩余数量
- 验证用户的领取资格
- 确保不超过用户的领取限制

### 5.2 自动发放规则

#### 5.2.1 发券规则管理
1. 导航至「优惠券」→「发券规则管理」
2. 点击「新增」创建发券规则

#### 5.2.2 规则配置
- **规则名称**：输入规则识别名称
- **触发条件**：设置自动发券的触发条件
- **关联优惠券**：选择要自动发放的优惠券
- **发放数量**：设置每次发放的数量
- **规则状态**：启用或禁用规则

### 5.3 发放记录查看

#### 5.3.1 使用记录列表
- 显示所有优惠券的发放和使用记录
- 包含用户信息、优惠券信息、批次信息
- 显示发放渠道和使用状态

#### 5.3.2 记录筛选
- 按用户筛选
- 按优惠券类型筛选
- 按发放时间筛选
- 按使用状态筛选

## 6. 操作界面说明

### 6.1 优惠券列表页面

#### 6.1.1 页面布局
- **搜索区域**：优惠券名称、类型筛选
- **操作按钮**：新增优惠券、批次管理
- **数据表格**：显示优惠券列表信息
- **分页组件**：支持分页浏览

#### 6.1.2 表格字段
- **序号**：优惠券ID
- **名称**：优惠券名称和描述
- **类型**：现金券、折扣券、满减券
- **范围**：商品或订单
- **状态**：已启用、已禁用
- **操作**：编辑、启用/禁用、删除、查看批次

### 6.2 优惠券详情页面

#### 6.2.1 表单区域
- **基本信息**：类型、名称、描述、状态
- **类型特定字段**：根据优惠券类型动态显示
- **使用规则**：条件设置、使用限制、应用规则

#### 6.2.2 产品选择器
- **条件对象选择**：选择优惠券适用的产品
- **应用对象选择**：选择优惠作用的产品
- **互斥规则选择**：选择互斥的其他优惠券

### 6.3 批次管理页面

#### 6.3.1 批次列表
- **搜索筛选**：批次名称、状态筛选
- **批次信息**：ID、名称、数量、有效期
- **发放渠道**：以标签形式显示
- **操作按钮**：编辑、启用/禁用、删除

#### 6.3.2 批次详情弹窗
- **基本信息**：名称、描述、关联优惠券
- **数量时间**：批次数量、有效时长、时间范围
- **发放设置**：渠道、周期、数量限制
- **领取设置**：时间范围、周期限制

### 6.4 发放管理页面

#### 6.4.1 发放弹窗
- **优惠券选择**：支持搜索的下拉选择器
- **批次选择**：根据优惠券动态加载
- **用户选择**：支持多选的用户选择器
- **操作按钮**：取消、发放

#### 6.4.2 使用记录列表
- **记录信息**：用户、优惠券、批次、发放渠道
- **时间信息**：发放时间、使用时间
- **状态信息**：已发放、已使用、已过期

## 7. 注意事项和最佳实践

### 7.1 优惠券创建注意事项

#### 7.1.1 命名规范
- 使用清晰、描述性的优惠券名称
- 包含优惠类型和金额信息
- 避免使用特殊字符和过长名称

#### 7.1.2 规则设置
- 仔细设置使用条件，避免规则冲突
- 合理设置使用限制，防止滥用
- 测试优惠券规则的正确性

#### 7.1.3 金额设置
- 折扣券的折扣比例必须在0-1之间
- 现金券和满减券的金额应为正数
- 考虑设置最大优惠金额限制

### 7.2 批次管理最佳实践

#### 7.2.1 批次规划
- 根据营销活动合理规划批次数量
- 设置合适的有效期，避免过期浪费
- 考虑用户领取和使用的时间分布

#### 7.2.2 发放控制
- 设置合理的发放渠道和数量限制
- 监控批次使用情况，及时调整策略
- 预留一定数量用于特殊情况

#### 7.2.3 时间管理
- 确保生效时间早于领取开始时间
- 设置合理的领取时间窗口
- 考虑节假日和营销活动的时间安排

### 7.3 发放管理建议

#### 7.3.1 手动发放
- 仔细核对用户信息，避免误发
- 选择正确的批次，确保有足够数量
- 记录发放原因，便于后续跟踪

#### 7.3.2 自动发放
- 测试发券规则的触发条件
- 监控自动发放的执行情况
- 设置合理的发放频率限制

#### 7.3.3 发放监控
- 定期检查发放记录的准确性
- 监控优惠券的使用率和转化率
- 分析发放效果，优化发放策略

### 7.4 系统维护建议

#### 7.4.1 数据备份
- 定期备份优惠券和批次数据
- 保存重要的发放记录
- 建立数据恢复机制

#### 7.4.2 性能优化
- 定期清理过期的优惠券数据
- 优化数据库查询性能
- 监控系统资源使用情况

#### 7.4.3 安全考虑
- 设置合适的权限控制
- 记录重要操作的审计日志
- 防止优惠券被恶意刷取

### 7.5 常见问题处理

#### 7.5.1 优惠券无法使用
- 检查优惠券状态是否启用
- 验证使用条件是否满足
- 确认优惠券是否在有效期内
- 检查用户是否超过使用限制

#### 7.5.2 批次数量不足
- 检查批次剩余数量
- 考虑创建新的批次
- 调整发放策略和数量限制

#### 7.5.3 发放失败
- 验证用户信息的正确性
- 检查批次的有效性和状态
- 确认用户的领取资格
- 查看系统错误日志

## 8. 技术支持

### 8.1 系统要求
- **浏览器**：Chrome 70+、Firefox 65+、Safari 12+
- **网络**：稳定的互联网连接
- **权限**：相应的优惠券管理权限

### 8.2 联系方式
如遇到技术问题或需要功能支持，请联系系统管理员或技术支持团队。

### 8.3 更新日志
系统会定期更新功能和修复问题，请关注版本更新说明。

---

*本说明书基于yh-vegan-admin-web项目编写，涵盖了优惠券系统的完整功能和操作流程。如有疑问或建议，欢迎反馈。*